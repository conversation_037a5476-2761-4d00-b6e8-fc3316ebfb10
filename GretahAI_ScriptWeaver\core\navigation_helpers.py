"""
Navigation helper functions for the GretahAI ScriptWeaver sidebar navigation system.

This module provides functions for:
1. Validating stage accessibility based on prerequisites
2. Determining stage status (completed, current, available, locked)
3. Creating visual indicators for navigation UI
4. Handling navigation constraints and user feedback
"""

import logging
from typing import Dict, List, Tuple, Optional
from state_manager import StateStage

# Set up logging
logger = logging.getLogger(__name__)


def get_stage_accessibility(state, target_stage: StateStage) -> Dict[str, any]:
    """
    Determine if a stage is accessible and get its status information.

    Args:
        state: StateManager instance
        target_stage: The stage to check accessibility for

    Returns:
        Dictionary containing:
        - accessible: bool - Whether the stage can be navigated to
        - status: str - 'completed', 'current', 'available', 'locked'
        - reason: str - Explanation for the status
        - missing_prerequisites: List[str] - What's needed to access this stage
    """
    try:
        current_stage_num = state.current_stage.get_stage_number()
        target_stage_num = target_stage.get_stage_number()

        # HOME, Stage 9 (<PERSON><PERSON><PERSON> Browser) and Stage 10 (Template Manager) are always accessible regardless of prerequisites
        if target_stage == StateStage.HOME:
            return {
                'accessible': True,
                'status': 'available',
                'reason': 'Home page is always accessible',
                'missing_prerequisites': []
            }

        if target_stage == StateStage.STAGE9_BROWSE:
            return {
                'accessible': True,
                'status': 'available',
                'reason': 'Script Browser is always accessible',
                'missing_prerequisites': []
            }

        if target_stage == StateStage.STAGE10_PLAYGROUND:
            return {
                'accessible': True,
                'status': 'available',
                'reason': 'Script Playground is always accessible',
                'missing_prerequisites': []
            }

        # Check stage completion and prerequisites first
        stage_info = _get_stage_prerequisites_info(state, target_stage)

        # Determine the appropriate stage based on completion
        # This helps identify if the current stage should actually be different
        appropriate_stage = _determine_appropriate_current_stage(state)
        appropriate_stage_num = appropriate_stage.get_stage_number()

        # Check if this is the current stage (either actual current or should be current)
        if target_stage_num == current_stage_num or target_stage_num == appropriate_stage_num:
            return {
                'accessible': True,
                'status': 'current',
                'reason': 'This is the current active stage',
                'missing_prerequisites': []
            }

        # Determine accessibility based on prerequisites
        if stage_info['all_prerequisites_met']:
            if target_stage_num < max(current_stage_num, appropriate_stage_num):
                status = 'completed'
                reason = 'Stage completed and accessible'
            else:
                status = 'available'
                reason = 'Prerequisites met, stage accessible'

            return {
                'accessible': True,
                'status': status,
                'reason': reason,
                'missing_prerequisites': []
            }
        else:
            return {
                'accessible': False,
                'status': 'locked',
                'reason': f"Missing prerequisites: {', '.join(stage_info['missing_prerequisites'])}",
                'missing_prerequisites': stage_info['missing_prerequisites']
            }

    except Exception as e:
        logger.error(f"Error checking stage accessibility for {target_stage}: {e}")
        return {
            'accessible': False,
            'status': 'locked',
            'reason': f"Error checking accessibility: {str(e)}",
            'missing_prerequisites': ['Unknown error']
        }


def _get_stage_prerequisites_info(state, target_stage: StateStage) -> Dict[str, any]:
    """
    Get detailed prerequisite information for a specific stage.

    Args:
        state: StateManager instance
        target_stage: The stage to check prerequisites for

    Returns:
        Dictionary with prerequisite information
    """
    missing_prerequisites = []

    # Stage 1: Always accessible (file upload)
    if target_stage == StateStage.STAGE1_UPLOAD:
        return {
            'all_prerequisites_met': True,
            'missing_prerequisites': []
        }

    # Stage 2: Requires file upload
    if target_stage == StateStage.STAGE2_WEBSITE:
        if not (hasattr(state, 'uploaded_excel') and state.uploaded_excel) and \
           not (hasattr(state, 'uploaded_file') and state.uploaded_file):
            missing_prerequisites.append('CSV file upload')

    # Stage 3: Requires file upload + website URL
    elif target_stage == StateStage.STAGE3_CONVERT:
        if not (hasattr(state, 'uploaded_excel') and state.uploaded_excel) and \
           not (hasattr(state, 'uploaded_file') and state.uploaded_file):
            missing_prerequisites.append('CSV file upload')

        if not hasattr(state, 'website_url') or not state.website_url or \
           state.website_url == "https://example.com":
            missing_prerequisites.append('Website URL configuration')

    # Stage 4: Requires Stage 3 + test case selection and conversion
    elif target_stage == StateStage.STAGE4_DETECT:
        # Check Stage 2 prerequisites first
        stage2_info = _get_stage_prerequisites_info(state, StateStage.STAGE2_WEBSITE)
        missing_prerequisites.extend(stage2_info['missing_prerequisites'])

        if not hasattr(state, 'website_url') or not state.website_url or \
           state.website_url == "https://example.com":
            missing_prerequisites.append('Website URL configuration')

        if not hasattr(state, 'selected_test_case') or not state.selected_test_case:
            missing_prerequisites.append('Test case selection')

        if not hasattr(state, 'conversion_done') or not state.conversion_done:
            missing_prerequisites.append('Test case conversion')

        if not hasattr(state, 'step_table_json') or not state.step_table_json:
            missing_prerequisites.append('Step table generation')

    # Stage 5: Requires Stage 4 + step selection and UI element matching
    elif target_stage == StateStage.STAGE5_DATA:
        # Check Stage 4 prerequisites first
        stage4_info = _get_stage_prerequisites_info(state, StateStage.STAGE4_DETECT)
        missing_prerequisites.extend(stage4_info['missing_prerequisites'])

        if not hasattr(state, 'selected_step') or not state.selected_step:
            missing_prerequisites.append('Step selection')

        if not hasattr(state, 'step_matches') or not state.step_matches:
            if not hasattr(state, 'element_matches') or not state.element_matches:
                missing_prerequisites.append('UI element detection')

    # Stage 6: Requires Stage 5 + test data configuration
    elif target_stage == StateStage.STAGE6_GENERATE:
        # Check Stage 5 prerequisites first
        stage5_info = _get_stage_prerequisites_info(state, StateStage.STAGE5_DATA)
        missing_prerequisites.extend(stage5_info['missing_prerequisites'])

        if not hasattr(state, 'test_data') or not state.test_data:
            if not hasattr(state, 'test_data_skipped') or not state.test_data_skipped:
                missing_prerequisites.append('Test data configuration')

    # Stage 7: Requires Stage 6 + script generation
    elif target_stage == StateStage.STAGE7_EXECUTE:
        # Check Stage 6 prerequisites first
        stage6_info = _get_stage_prerequisites_info(state, StateStage.STAGE6_GENERATE)
        missing_prerequisites.extend(stage6_info['missing_prerequisites'])

        if not hasattr(state, 'generated_script_path') or not state.generated_script_path:
            missing_prerequisites.append('Script generation')

    # Stage 8: Requires Stage 7 + all steps completion
    elif target_stage == StateStage.STAGE8_OPTIMIZE:
        # Check Stage 7 prerequisites first
        stage7_info = _get_stage_prerequisites_info(state, StateStage.STAGE7_EXECUTE)
        missing_prerequisites.extend(stage7_info['missing_prerequisites'])

        if not hasattr(state, 'all_steps_done') or not state.all_steps_done:
            missing_prerequisites.append('All test steps completion')

    # Stage 9: Always accessible (script browser utility)
    elif target_stage == StateStage.STAGE9_BROWSE:
        # Stage 9 is always accessible as it's a utility feature
        # Empty state is handled gracefully within the stage itself
        pass

    # Stage 10: Always accessible (script playground utility)
    elif target_stage == StateStage.STAGE10_PLAYGROUND:
        # Stage 10 is always accessible as it's a utility feature
        # Empty state is handled gracefully within the stage itself
        pass

    # Remove duplicates while preserving order
    missing_prerequisites = list(dict.fromkeys(missing_prerequisites))

    return {
        'all_prerequisites_met': len(missing_prerequisites) == 0,
        'missing_prerequisites': missing_prerequisites
    }


def get_stage_visual_indicator(accessibility_info: Dict[str, any]) -> str:
    """
    Get the visual indicator (emoji) for a stage based on its accessibility info.

    Args:
        accessibility_info: Dictionary from get_stage_accessibility()

    Returns:
        String emoji indicator
    """
    status = accessibility_info.get('status', 'locked')

    if status == 'completed':
        return '✅'
    elif status == 'current':
        return '🔶'
    elif status == 'available':
        return '⚠️'
    else:  # locked
        return '⬜'


def get_stage_button_style(accessibility_info: Dict[str, any]) -> str:
    """
    Get the Streamlit button type for a stage based on its accessibility.

    Args:
        accessibility_info: Dictionary from get_stage_accessibility()

    Returns:
        String button type for Streamlit
    """
    status = accessibility_info.get('status', 'locked')
    accessible = accessibility_info.get('accessible', False)

    if status == 'current':
        return 'primary'
    elif accessible:
        return 'secondary'
    else:
        return 'secondary'  # Disabled buttons will be handled separately


def format_stage_tooltip(stage: StateStage, accessibility_info: Dict[str, any]) -> str:
    """
    Create a tooltip/help text for a stage navigation button.

    Args:
        stage: The StateStage enum
        accessibility_info: Dictionary from get_stage_accessibility()

    Returns:
        Formatted tooltip string
    """
    stage_name = stage.get_display_name()
    status = accessibility_info.get('status', 'locked')
    reason = accessibility_info.get('reason', '')
    missing = accessibility_info.get('missing_prerequisites', [])

    tooltip = f"**{stage_name}**\n\n"

    if status == 'completed':
        tooltip += "✅ **Completed** - Click to revisit this stage"
    elif status == 'current':
        tooltip += "🔶 **Current Stage** - You are here"
    elif status == 'available':
        tooltip += "⚠️ **Available** - Click to navigate to this stage"
    else:  # locked
        tooltip += "⬜ **Locked** - Complete prerequisites first"
        if missing:
            tooltip += f"\n\n**Missing:**\n" + "\n".join(f"• {item}" for item in missing)

    return tooltip


def validate_navigation_safety(state, target_stage: StateStage) -> Tuple[bool, str]:
    """
    Validate if navigation to a target stage is safe and won't cause data loss.

    Args:
        state: StateManager instance
        target_stage: The stage to navigate to

    Returns:
        Tuple of (is_safe: bool, warning_message: str)
    """
    current_stage_num = state.current_stage.get_stage_number()
    target_stage_num = target_stage.get_stage_number()

    # Forward navigation is generally safe
    if target_stage_num > current_stage_num:
        return True, ""

    # Same stage navigation is safe
    if target_stage_num == current_stage_num:
        return True, ""

    # Backward navigation warnings
    if target_stage_num < current_stage_num:
        # Check for potential data loss scenarios
        warnings = []

        # Warn about losing step progress
        if current_stage_num >= 4 and target_stage_num <= 3:
            if hasattr(state, 'selected_step') and state.selected_step:
                warnings.append("current step selection")

        # Warn about losing script generation progress
        if current_stage_num >= 6 and target_stage_num <= 5:
            if hasattr(state, 'generated_script_path') and state.generated_script_path:
                warnings.append("generated scripts")

        # Warn about losing test execution progress
        if current_stage_num >= 7 and target_stage_num <= 6:
            if hasattr(state, 'completed_steps') and state.completed_steps:
                warnings.append("test execution progress")

        if warnings:
            warning_msg = f"⚠️ Going back may affect: {', '.join(warnings)}. Your data will be preserved, but you may need to reconfigure some settings."
            return True, warning_msg

    return True, ""


def _determine_appropriate_current_stage(state) -> StateStage:
    """
    Determine what the current stage should be based on completion status.

    This is similar to StateManager.update_stage_based_on_completion but
    doesn't modify the state, just returns the appropriate stage.

    Args:
        state: StateManager instance

    Returns:
        StateStage that should be the current stage
    """
    # Start with Stage 1 as default
    target_stage = StateStage.STAGE1_UPLOAD

    # Check Stage 1 completion (file uploaded)
    if (hasattr(state, 'uploaded_excel') and state.uploaded_excel) or \
       (hasattr(state, 'uploaded_file') and state.uploaded_file):
        target_stage = StateStage.STAGE2_WEBSITE

        # Check Stage 2 completion (website URL configured)
        if hasattr(state, 'website_url') and state.website_url and \
           state.website_url != "https://example.com":
            target_stage = StateStage.STAGE3_CONVERT

            # Check Stage 3 completion (test case selected and converted)
            if hasattr(state, 'selected_test_case') and state.selected_test_case and \
               hasattr(state, 'conversion_done') and state.conversion_done and \
               hasattr(state, 'step_table_json') and state.step_table_json:
                target_stage = StateStage.STAGE4_DETECT

                # Check Stage 4 completion (step selected and elements matched)
                if hasattr(state, 'selected_step') and state.selected_step and \
                   ((hasattr(state, 'step_matches') and state.step_matches) or \
                    (hasattr(state, 'element_matches') and state.element_matches)):
                    target_stage = StateStage.STAGE5_DATA

                    # Check Stage 5 completion (test data configured or skipped)
                    if (hasattr(state, 'test_data') and state.test_data) or \
                       (hasattr(state, 'test_data_skipped') and state.test_data_skipped):
                        target_stage = StateStage.STAGE6_GENERATE

                        # Check Stage 6 completion (script generated)
                        if hasattr(state, 'generated_script_path') and state.generated_script_path:
                            target_stage = StateStage.STAGE7_EXECUTE

                            # Check Stage 7 completion (all steps done)
                            if hasattr(state, 'all_steps_done') and state.all_steps_done:
                                target_stage = StateStage.STAGE8_OPTIMIZE

                                # Check Stage 8 completion (optimization done)
                                if hasattr(state, 'optimization_complete') and state.optimization_complete:
                                    # Could go to Stage 9 or back to Stage 3 for new test case
                                    # Default to Stage 8 as completed
                                    target_stage = StateStage.STAGE8_OPTIMIZE

    return target_stage


def get_all_stages_navigation_info(state) -> Dict[StateStage, Dict[str, any]]:
    """
    Get navigation information for all stages at once.

    Args:
        state: StateManager instance

    Returns:
        Dictionary mapping each StateStage to its accessibility info
    """
    all_stages = [
        StateStage.HOME,
        StateStage.STAGE1_UPLOAD,
        StateStage.STAGE2_WEBSITE,
        StateStage.STAGE3_CONVERT,
        StateStage.STAGE4_DETECT,
        StateStage.STAGE5_DATA,
        StateStage.STAGE6_GENERATE,
        StateStage.STAGE7_EXECUTE,
        StateStage.STAGE8_OPTIMIZE,
        StateStage.STAGE9_BROWSE,
        StateStage.STAGE10_PLAYGROUND
    ]

    navigation_info = {}
    for stage in all_stages:
        navigation_info[stage] = get_stage_accessibility(state, stage)

    return navigation_info
