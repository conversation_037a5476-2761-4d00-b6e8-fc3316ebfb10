"""
Pure helper functions for ScriptWeaver

This module contains stateless utility functions that don't depend on Streamlit's
session state or other application-specific state. These functions can be used
independently of the main application flow.
"""

import os
import json
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ScriptWeaver.helpers_pure")

# Define the directory where app.py and other files are located.
# This is the current directory of this helpers_pure.py file.
current_app_dir = os.path.dirname(os.path.abspath(__file__))
APP_CONFIG_FILE = os.path.join(current_app_dir, "config.json")

def load_google_api_key():
    """
    Load Google API key from config.json or environment variable.

    This function attempts to load the Google API key from multiple sources
    in the following priority order:
    1. Environment variable GOOGLE_API_KEY
    2. Local config.json file in the application directory

    Returns:
        str: The Google API key if found, or an empty string if not found
    """
    try:
        # Priority 1: Environment variable
        env_api_key = os.environ.get("GOOGLE_API_KEY")
        if (env_api_key):
            logger.info("Found Google API key in environment variable GOOGLE_API_KEY")
            return env_api_key

        # Priority 2: App's local config.json (config.json next to app.py)
        if os.path.exists(APP_CONFIG_FILE):
            try:
                with open(APP_CONFIG_FILE, "r") as f:
                    config = json.load(f)
                api_key = config.get("google_api_key", "")
                if (api_key):
                    logger.info(f"Found Google API key in {APP_CONFIG_FILE}")
                    return api_key
            except Exception as e:
                logger.warning(f"Notice: Error reading {APP_CONFIG_FILE} (this is okay if key is found elsewhere): {e}")

        # If we get here, no API key was found
        logger.warning("No Google API key found in any config file or environment variable")
        return ""
    except Exception as e:
        logger.error(f"Error loading Google API key: {e}")
        return ""

def analyze_step_table(step_table_data):
    """
    Analyze the automation-ready step table data and determine if UI element detection is needed.

    Args:
        step_table_data (tuple): A tuple containing (markdown_table, json_table) where:
            - markdown_table (str): The automation-ready step table in markdown format
            - json_table (list): The step table as a list of dictionaries

    Returns:
        dict: A dictionary containing:
            - requires_ui_elements (bool): Whether UI element detection is needed
            - reason (str): The reason why UI element detection is or isn't needed
            - locator_strategies (list): List of locator strategies used in the table
            - actions (list): List of actions used in the table
    """
    # Extract markdown table and JSON table from the input
    if isinstance(step_table_data, tuple) and len(step_table_data) == 2:
        markdown_table, json_table = step_table_data
    else:
        # If not a tuple, assume it's just the markdown table (for backward compatibility)
        markdown_table = step_table_data
        json_table = []

    # If JSON table is empty or invalid, try to parse from markdown
    if not json_table or not isinstance(json_table, list):
        # Fall back to the old parsing method if needed
        return parse_step_table_markdown(markdown_table)

    # Extract actions and locator strategies from the JSON table
    actions = []
    locator_strategies = []

    for row in json_table:
        if not isinstance(row, dict):
            continue

        # Extract action and locator strategy
        action = row.get('action', '').lower()
        locator_strategy = row.get('locator_strategy', '').lower()

        if action:
            actions.append(action)
        if locator_strategy:
            locator_strategies.append(locator_strategy)

    # Determine if UI element detection is needed
    requires_ui_elements = True
    reason = "This step requires UI element detection for proper automation."

    # Check if all actions are navigation-only or if all locator strategies are "url" or empty
    navigation_actions = ["navigate", "go to", "open", "visit", "browse to"]

    # Enhanced detection for navigation-related actions
    is_navigation_only = True
    for action in actions:
        if action:
            # Check if any navigation term is in the action
            contains_navigation_term = False
            for nav in navigation_actions:
                if nav in action:
                    contains_navigation_term = True
                    break

            # Additional check for "verify" + "navigate" patterns
            if not contains_navigation_term and "verify" in action and any(nav in action for nav in navigation_actions):
                contains_navigation_term = True

            # If this action doesn't contain any navigation terms, mark as not navigation-only
            if not contains_navigation_term:
                is_navigation_only = False
                break

    if is_navigation_only and actions:
        requires_ui_elements = False
        reason = "This step only involves navigation actions which don't require UI element detection."
    elif all(strategy in ["url", "none", "n/a", ""] for strategy in locator_strategies if strategy):
        requires_ui_elements = False
        reason = "This step doesn't use any UI element locators that would require detection."

    # Add debugging information
    logger.debug(f"Actions detected: {actions}")
    logger.debug(f"Locator strategies detected: {locator_strategies}")
    logger.debug(f"Requires UI elements: {requires_ui_elements}")
    logger.debug(f"Reason: {reason}")

    return {
        "requires_ui_elements": requires_ui_elements,
        "reason": reason,
        "locator_strategies": locator_strategies,
        "actions": actions
    }

def analyze_step_for_test_data(step_table_entry, action_text=None):
    """
    Analyze a test step to determine if it requires test data configuration.

    Args:
        step_table_entry (dict): The step table entry for the step
        action_text (str, optional): The original action text from the test step

    Returns:
        dict: A dictionary containing:
            - requires_test_data (bool): Whether test data configuration is needed
            - reason (str): The reason why test data is or isn't needed
            - data_types (list): List of data types that might be needed
    """
    # Initialize with default values
    requires_test_data = False
    reason = "This step does not appear to require any test data."
    data_types = []

    # Check if step_table_entry is valid
    if not step_table_entry or not isinstance(step_table_entry, dict):
        return {
            "requires_test_data": True,
            "reason": "Could not analyze step. Defaulting to requiring test data.",
            "data_types": []
        }

    # Check action type in step table entry
    action_type = step_table_entry.get('action', '').lower()
    step_type = step_table_entry.get('step_type', '').lower()

    # First, check if this is a navigation step
    navigation_actions = ["navigate", "go to", "open", "visit", "browse to"]
    url_assertions = ["url_equals", "url_contains", "url_matches"]
    assertion_type = step_table_entry.get('assertion_type', '').lower()

    # Check if this is a navigation step based on action, step type, or assertion type
    is_navigation_step = (
        any(nav in action_type for nav in navigation_actions) or
        'navigation' in step_type or
        assertion_type in url_assertions
    )

    # For navigation steps, URL parameters are handled differently and don't require test data configuration
    if is_navigation_step:
        # Even if there's a test_data_param, navigation steps don't need test data configuration
        return {
            "requires_test_data": False,
            "reason": "This is a navigation step. URL parameters are handled automatically and don't require test data configuration.",
            "data_types": []
        }

    # For non-navigation steps, continue with normal analysis

    # Check for explicit test_data_param in step table entry
    test_data_param = step_table_entry.get('test_data_param', '')
    if test_data_param and test_data_param.lower() not in ['none', 'n/a', '']:
        requires_test_data = True
        reason = f"This step requires test data for parameter: {test_data_param}"
        data_types.append(test_data_param)

    # Actions that typically require test data
    data_input_actions = ['type', 'input', 'enter', 'fill', 'write', 'submit', 'select', 'choose']

    # Check if action contains any data input keywords
    if any(keyword in action_type for keyword in data_input_actions):
        requires_test_data = True
        reason = f"This step involves data input: {action_type}"

    # Check step type for form-related operations
    if 'form' in step_type or 'input' in step_type or 'data' in step_type:
        requires_test_data = True
        reason = f"This step involves form interaction: {step_type}"

    # Check original action text for data input indicators if provided
    if action_text:
        action_lower = action_text.lower()

        # Check for common phrases that indicate data input
        if any(phrase in action_lower for phrase in ['enter', 'type', 'input', 'fill in', 'provide']):
            requires_test_data = True
            reason = "This step requires entering data based on the action description."

            # Try to identify what kind of data might be needed
            data_indicators = {
                'email': ['email', 'e-mail', 'mail address'],
                'password': ['password', 'pwd', 'passphrase'],
                'username': ['username', 'user name', 'login name', 'user id'],
                'name': ['name', 'full name', 'first name', 'last name'],
                'phone': ['phone', 'mobile', 'telephone', 'cell'],
                'address': ['address', 'street', 'city', 'state', 'zip', 'postal'],
                'payment': ['payment', 'credit card', 'card number', 'cvv', 'expiry', 'expiration'],
                'search': ['search', 'find', 'query', 'look for'],
                'date': ['date', 'calendar', 'day', 'month', 'year', 'dob', 'birth']
            }

            for data_type, indicators in data_indicators.items():
                if any(indicator in action_lower for indicator in indicators):
                    data_types.append(data_type)

    # If we haven't found any data types but determined data is required, add a generic type
    if requires_test_data and not data_types:
        data_types.append('generic_input')

    return {
        "requires_test_data": requires_test_data,
        "reason": reason,
        "data_types": data_types
    }

def parse_step_table_markdown(step_table):
    """
    Parse the automation-ready step table in markdown format and determine if UI element detection is needed.
    This is a fallback method when JSON parsing fails.

    Args:
        step_table (str): The markdown step table generated by the AI

    Returns:
        dict: A dictionary containing:
            - requires_ui_elements (bool): Whether UI element detection is needed
            - reason (str): The reason why UI element detection is or isn't needed
            - locator_strategies (list): List of locator strategies used in the table
            - actions (list): List of actions used in the table
    """
    if not step_table or not isinstance(step_table, str):
        return {
            "requires_ui_elements": True,
            "reason": "Could not analyze step table. Defaulting to requiring UI elements.",
            "locator_strategies": [],
            "actions": []
        }

    # Extract table rows (skip header and separator rows)
    rows = step_table.strip().split('\n')
    if len(rows) < 3:  # Need at least header, separator, and one data row
        return {
            "requires_ui_elements": True,
            "reason": "Step table has insufficient data. Defaulting to requiring UI elements.",
            "locator_strategies": [],
            "actions": []
        }

    # Skip header and separator rows
    data_rows = rows[2:]

    # Parse each row to extract action and locator strategy
    actions = []
    locator_strategies = []

    for row in data_rows:
        # Split by pipe character and strip whitespace
        cells = [cell.strip() for cell in row.split('|')]
        if len(cells) < 7:  # Need at least 7 cells (including empty cells at start/end)
            continue

        # Extract action and locator strategy (adjust indices if needed)
        action = cells[2] if len(cells) > 2 else ""
        locator_strategy = cells[3] if len(cells) > 3 else ""

        if action:
            actions.append(action.lower())
        if locator_strategy:
            locator_strategies.append(locator_strategy.lower())

    # Determine if UI element detection is needed
    requires_ui_elements = True
    reason = "This step requires UI element detection for proper automation."

    # Check if all actions are navigation-only or if all locator strategies are "url" or empty
    navigation_actions = ["navigate", "go to", "open", "visit", "browse to"]

    # Enhanced detection for navigation-related actions
    is_navigation_only = True
    for action in actions:
        if action:
            # Check if any navigation term is in the action
            contains_navigation_term = False
            for nav in navigation_actions:
                if nav in action:
                    contains_navigation_term = True
                    break

            # Additional check for "verify" + "navigate" patterns
            if not contains_navigation_term and "verify" in action and any(nav in action for nav in navigation_actions):
                contains_navigation_term = True

            # If this action doesn't contain any navigation terms, mark as not navigation-only
            if not contains_navigation_term:
                is_navigation_only = False
                break

    if is_navigation_only and actions:
        requires_ui_elements = False
        reason = "This step only involves navigation actions which don't require UI element detection."
    elif all(strategy in ["url", "none", "n/a", ""] for strategy in locator_strategies if strategy):
        requires_ui_elements = False
        reason = "This step doesn't use any UI element locators that would require detection."

    return {
        "requires_ui_elements": requires_ui_elements,
        "reason": reason,
        "locator_strategies": locator_strategies,
        "actions": actions
    }
