#!/usr/bin/env python3
"""
Test script to verify Stage 10's automatic API key loading functionality.

This script tests:
1. Loading API key from config.json
2. Fallback to environment variable
3. Manual input as final fallback
4. Prerequisite validation with multiple sources
"""

import os
import sys
import json
import tempfile
from pathlib import Path

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import Stage 10 functions
from stages.stage10 import load_stage10_google_api_key, _validate_stage10_prerequisites
from core.config import APP_CONFIG_FILE

class MockState:
    """Mock state manager for testing."""
    def __init__(self):
        self.stage10_google_api_key = ""
        self._script_storage = MockScriptStorage()

class MockScriptStorage:
    """Mock script storage for testing."""
    pass

def test_config_loading():
    """Test automatic loading from config.json."""
    print("🧪 Testing Stage 10 API Key Loading...")
    
    # Test 1: Load from existing config.json
    print("\n1. Testing config.json loading...")
    if os.path.exists(APP_CONFIG_FILE):
        try:
            with open(APP_CONFIG_FILE, 'r') as f:
                config = json.load(f)
            
            if 'google_api_key' in config and config['google_api_key']:
                print(f"✅ Config file exists: {APP_CONFIG_FILE}")
                print(f"✅ API key found in config: {config['google_api_key'][:8]}...")
                
                # Test the loading function
                loaded_key = load_stage10_google_api_key()
                if loaded_key == config['google_api_key']:
                    print("✅ load_stage10_google_api_key() correctly loaded from config")
                else:
                    print("❌ load_stage10_google_api_key() failed to load from config")
            else:
                print("⚠️ Config file exists but no API key found")
        except Exception as e:
            print(f"❌ Error reading config file: {e}")
    else:
        print(f"⚠️ Config file not found: {APP_CONFIG_FILE}")
    
    # Test 2: Environment variable fallback
    print("\n2. Testing environment variable fallback...")
    original_env = os.environ.get('GOOGLE_API_KEY', '')
    
    # Temporarily set environment variable
    test_env_key = "AIzaSyTestEnvironmentKey123456789"
    os.environ['GOOGLE_API_KEY'] = test_env_key
    
    # Clear cache to test environment fallback
    load_stage10_google_api_key.clear()
    
    # Temporarily rename config file to test env fallback
    config_backup = None
    if os.path.exists(APP_CONFIG_FILE):
        config_backup = APP_CONFIG_FILE + ".backup"
        os.rename(APP_CONFIG_FILE, config_backup)
    
    try:
        loaded_key = load_stage10_google_api_key()
        if loaded_key == test_env_key:
            print("✅ Environment variable fallback works correctly")
        else:
            print(f"❌ Environment variable fallback failed: got '{loaded_key}', expected '{test_env_key}'")
    finally:
        # Restore original environment
        if original_env:
            os.environ['GOOGLE_API_KEY'] = original_env
        else:
            os.environ.pop('GOOGLE_API_KEY', None)
        
        # Restore config file
        if config_backup and os.path.exists(config_backup):
            os.rename(config_backup, APP_CONFIG_FILE)
        
        # Clear cache
        load_stage10_google_api_key.clear()
    
    # Test 3: Prerequisite validation
    print("\n3. Testing prerequisite validation...")
    
    # Test with API key available
    state = MockState()
    state.stage10_google_api_key = "AIzaSyTestKey123456789"
    
    is_valid, error_msg = _validate_stage10_prerequisites(state)
    if is_valid:
        print("✅ Prerequisite validation passes with API key")
    else:
        print(f"❌ Prerequisite validation failed: {error_msg}")
    
    # Test without API key
    state_no_key = MockState()
    state_no_key.stage10_google_api_key = ""
    
    # Clear environment to ensure no fallback
    original_env = os.environ.get('GOOGLE_API_KEY', '')
    os.environ.pop('GOOGLE_API_KEY', None)
    load_stage10_google_api_key.clear()
    
    try:
        is_valid, error_msg = _validate_stage10_prerequisites(state_no_key)
        if not is_valid and "API key" in error_msg:
            print("✅ Prerequisite validation correctly fails without API key")
        else:
            print(f"❌ Prerequisite validation should fail without API key: {is_valid}, {error_msg}")
    finally:
        # Restore environment
        if original_env:
            os.environ['GOOGLE_API_KEY'] = original_env
        load_stage10_google_api_key.clear()
    
    print("\n🎉 Stage 10 API Key Loading Tests Complete!")

if __name__ == "__main__":
    test_config_loading()
