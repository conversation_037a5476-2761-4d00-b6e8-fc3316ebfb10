"""
Pages package for GretahAI ScriptWeaver

This package contains page components that are separate from the sequential workflow stages.
These pages provide navigation, overview, and utility functions.

Pages:
- home.py: Central navigation hub and application landing page
"""

# Import page functions
from pages.home import render_home_page

# Export page functions
__all__ = [
    'render_home_page'
]
