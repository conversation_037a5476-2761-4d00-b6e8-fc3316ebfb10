"""
Element matching module for GretahAI ScriptWeaver.

This module provides functions for matching test steps to UI elements.
It implements the match_elements_with_ai function that was previously in core.py.
"""

import os
import sys
import json
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ScriptWeaver.core.match_elements")

# Add the parent directory to the path so we can import from parent modules
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

# Try to import the AI functions
try:
    from .ai import generate_llm_response, initialize_ai_client
    logger.info("Successfully imported AI functions")
except ImportError as e:
    logger.error(f"Error importing AI functions: {e}")

    # Define fallback functions
    def generate_llm_response(prompt, model_name=None):
        logger.warning("Using fallback generate_llm_response function")
        return "{}"

    def initialize_ai_client(api_key=None):
        logger.warning("Using fallback initialize_ai_client function")
        return False

def match_elements_with_ai(test_cases, elements, api_key=None):
    """
    Match test steps to UI elements using AI.

    Args:
        test_cases (dict or list): Test case(s) to match elements for
        elements (list): List of UI elements to match against
        api_key (str, optional): API key for Google AI. If None, use initialized client

    Returns:
        dict: Dictionary mapping test steps to matched elements
    """
    logger.info(f"Matching elements for test case(s)")

    # Handle input format
    if isinstance(test_cases, dict):
        test_cases = [test_cases]
    elif not isinstance(test_cases, list):
        logger.error(f"Invalid test case format: {type(test_cases)}")
        return {}

    # Check for empty inputs
    if not test_cases or not elements:
        logger.warning("Empty test cases or elements list")
        return {}

    # Initialize AI client if API key is provided
    if api_key:
        initialize_ai_client(api_key)
    elif not initialize_ai_client():
        logger.warning("Failed to initialize AI client")
        return {}

    matches = {}
    for test_case in test_cases:
        try:
            if not isinstance(test_case, dict):
                logger.warning(f"Skipping non-dict test case: {type(test_case)}")
                continue

            # Get test case ID
            tc_id = test_case.get('Test Case ID') or test_case.get('id')
            if not tc_id:
                logger.warning("Skipping test case with no ID")
                continue

            matches[tc_id] = {}

            # Get steps from the test case using standardized key
            steps = test_case.get('Steps', [])
            if not isinstance(steps, list):
                logger.warning(f"Skipping test case {tc_id} with invalid steps format: {type(steps)}")
                continue

            # Process each step
            for i, step in enumerate(steps):
                if not isinstance(step, dict):
                    logger.warning(f"Skipping non-dict step in test case {tc_id}: {type(step)}")
                    continue

                # Get step information
                step_num = step.get('Step No') or step.get('step_no') or str(i+1)
                action = step.get('Test Steps') or step.get('action', '')
                expected = step.get('Expected Result') or step.get('expected', '')

                # Create AI prompt for element matching
                prompt = f"""
                Analyze this test step and find matching UI elements if any are needed. If no UI element is required (e.g., navigation or page presence), return an empty matches list.
                Test Case: {tc_id}
                Step {step_num}:
                Action: {action}
                Expected: {expected}
                Available UI Elements (JSON array):
                {json.dumps(elements, indent=2)}
                For each relevant UI element, determine:
                1. How well it matches the step requirements (confidence score 0-1)
                2. What action should be performed (click, input, verify, clear, etc.)
                3. Any specific data or values needed
                Return ONLY valid JSON in this format (no explanation, no markdown):
                {{
                    "matches": [
                        {{
                            "element": {{"element object"}},
                            "score": float,
                            "action": string,
                            "data": string or null
                        }}
                    ]
                }}
                """

                try:
                    # Get AI response
                    response = generate_llm_response(prompt)

                    # Handle empty response
                    if not response or not response.strip():
                        logger.warning(f"Empty response for test case {tc_id}, step {step_num}")
                        matches[tc_id][str(step_num)] = []
                        continue

                    # Clean and parse response
                    try:
                        cleaned_response = response.strip()

                        # Handle markdown code blocks
                        if cleaned_response.startswith("```"):
                            cleaned_response = cleaned_response.lstrip('`')
                            if cleaned_response.lower().startswith("json"):
                                cleaned_response = cleaned_response[4:].strip()
                            elif cleaned_response.lower().startswith("python"):
                                cleaned_response = cleaned_response[6:].strip()
                            if cleaned_response.endswith("```"):
                                cleaned_response = cleaned_response[:-3].strip()

                        # Parse JSON response
                        matches_data = json.loads(cleaned_response)

                        # Extract matches
                        step_matches = []
                        for match in matches_data.get('matches', []):
                            step_matches.append(match)

                        # Sort matches by score
                        step_matches.sort(key=lambda x: x.get('score', 0), reverse=True)
                        matches[tc_id][str(step_num)] = step_matches

                    except Exception as parse_error:
                        logger.error(f"Error parsing AI response for test case {tc_id}, step {step_num}: {parse_error}")
                        matches[tc_id][str(step_num)] = []
                        continue

                except Exception as ai_error:
                    logger.error(f"Error getting AI response for test case {tc_id}, step {step_num}: {ai_error}")
                    matches[tc_id][str(step_num)] = []

        except Exception as tc_error:
            logger.error(f"Error processing test case: {tc_error}")
            continue

    return matches
