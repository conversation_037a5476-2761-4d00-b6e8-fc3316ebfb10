"""
Core module initialization.
Export main functions from submodules for easy access.
"""

import os
import sys
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("ScriptWeaver.core")

# Add the parent directory to the path so we can import from parent modules
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

# Import main functions from submodules
try:
    from .elements import find_ui_elements
    from .analysis import (
        map_test_steps_to_elements,
        analyze_test_case,
        filter_qa_relevant_elements,
        create_element_matching_prompt_with_context
    )
    from .ai import (
        initialize_ai_client,
        generate_llm_response,
        convert_test_case_to_step_table,
        generate_test_script,
        generate_test_script_prompt
    )

    # Import match_elements_with_ai from the new module
    try:
        from .match_elements import match_elements_with_ai
        logger.info("Successfully imported match_elements_with_ai from match_elements module")
    except ImportError as match_error:
        logger.error(f"Error importing match_elements_with_ai from match_elements module: {match_error}")
        # Print detailed traceback for debugging
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")

    # Import interactive selector with detailed error handling
    try:
        from .interactive_selector import select_element_interactively
        logger.info("Successfully imported interactive_selector module")
    except ImportError as selector_error:
        logger.error(f"Error importing interactive_selector module: {selector_error}")
        # Print detailed traceback for debugging
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")

    # We'll handle core.py imports differently to avoid circular imports
    logger.info("Skipping direct imports from core.py to avoid circular imports")

    logger.info("Successfully imported functions from core modules")
except ImportError as e:
    logger.error(f"Error importing functions from core modules: {e}")
    # Print detailed traceback for debugging
    import traceback
    logger.error(f"Traceback: {traceback.format_exc()}")

    # Create dummy functions for backward compatibility
    def detect_elements(url, browser=None, headless=True, save_to_file=True, elements_dir="detected_elements"):
        """Dummy detect_elements function that returns an empty list."""
        logger.error(f"Cannot detect UI elements on {url}: detect_elements function not available")
        return []

    def detect_elements_advanced(url, browser=None, headless=True, save_to_file=True, elements_dir="detected_elements"):
        """Dummy detect_elements_advanced function that returns an empty list."""
        logger.error(f"Cannot detect UI elements (advanced) on {url}: detect_elements_advanced function not available")
        return []

    def match_elements_with_ai(test_case, elements, api_key=None):
        """Dummy match_elements_with_ai function that returns an empty dictionary."""
        logger.error(f"Cannot match elements for test case: match_elements_with_ai function not available")
        return {}

    def select_element_interactively(url, browser=None, headless=False):
        """Dummy select_element_interactively function that returns None."""
        logger.error(f"Cannot open interactive element selector for {url}: select_element_interactively function not available")
        return None

    def find_ui_elements(*args, **kwargs):
        """Dummy find_ui_elements function that returns an empty list."""
        logger.error("Cannot find UI elements: find_ui_elements function not available")
        return []

    def map_test_steps_to_elements(*args, **kwargs):
        """Dummy map_test_steps_to_elements function that returns an empty dictionary."""
        logger.error("Cannot map test steps to elements: map_test_steps_to_elements function not available")
        return {}

    def analyze_test_case(*args, **kwargs):
        """Dummy analyze_test_case function that returns an empty dictionary."""
        logger.error("Cannot analyze test case: analyze_test_case function not available")
        return {}

    def filter_qa_relevant_elements(elements, *args, **kwargs):
        """Dummy filter_qa_relevant_elements function that returns the input elements."""
        logger.error("Cannot filter elements for QA relevance: filter_qa_relevant_elements function not available")
        return elements[:20] if elements else []

    def create_element_matching_prompt_with_context(*args, **kwargs):
        """Dummy create_element_matching_prompt_with_context function that returns an empty string."""
        logger.error("Cannot create element matching prompt: create_element_matching_prompt_with_context function not available")
        return ""

    def initialize_ai_client(*args, **kwargs):
        """Dummy initialize_ai_client function that returns False."""
        logger.error("Cannot initialize AI client: initialize_ai_client function not available")
        return False

    def generate_llm_response(*args, **kwargs):
        """Dummy generate_llm_response function that returns an empty string."""
        logger.error("Cannot generate LLM response: generate_llm_response function not available")
        return ""

    def convert_test_case_to_step_table(*args, **kwargs):
        """Dummy convert_test_case_to_step_table function that returns empty strings."""
        logger.error("Cannot convert test case to step table: convert_test_case_to_step_table function not available")
        return "", []

    def generate_test_script(*args, **kwargs):
        """Dummy generate_test_script function that returns an empty tuple (merged_script, step_specific_script)."""
        logger.error("Cannot generate test script: generate_test_script function not available")
        return "", ""

    def generate_test_script_prompt(*args, **kwargs):
        """Dummy generate_test_script_prompt function that returns an empty string."""
        logger.error("Cannot generate test script prompt: generate_test_script_prompt function not available")
        return ""

# Export main functions
__all__ = [
    'find_ui_elements',                       # Main function to find UI elements on a webpage
    'map_test_steps_to_elements',             # Main function to map test steps to UI elements
    'analyze_test_case',                      # Function to analyze a test case
    'initialize_ai_client',                   # Function to initialize AI client
    'generate_llm_response',                  # Function to generate a response from the LLM
    'filter_qa_relevant_elements',            # Function to filter elements for QA automation relevance
    'create_element_matching_prompt_with_context',  # Function to create prompt with step context
    'generate_test_script',                   # Function to generate test script
    'generate_test_script_prompt',            # Function to generate prompt for test script generation
    'convert_test_case_to_step_table',        # Function to convert test case to step table
    'detect_elements',                        # Backward compatibility function
    'detect_elements_advanced',               # Backward compatibility function
    'match_elements_with_ai',                 # Backward compatibility function
    'select_element_interactively'            # Function to select elements interactively
]
