# GretahAI TestInsight - Comprehensive Documentation

---

**© 2025 Cogniron. All Rights Reserved.**

**PROPRIETARY COMMERCIAL SOFTWARE** - This software is proprietary and confidential.

---

**Version:** 2.1.0
**Date:** May 28, 2025

## 1. Application Overview

GretahAI TestInsight is a proprietary Streamlit-based web application designed to streamline the process of test execution, log analysis, and root cause analysis (RCA) for software testing workflows. This commercial software integrates AI capabilities to provide deeper insights into test failures and log data.

**Core Functionalities:**

1.  **Test Suite Execution:**
    *   Upload and execute Python test suites using `pytest`.
    *   Monitor test execution in real-time by observing generated log files.
    *   Stop ongoing test executions.
2.  **Test Results Comparison:**
    *   Automatically parses JUnit XML results generated by `pytest`.
    *   Displays key metrics (Total, Passed, Failed) for the latest run.
    *   Compares the latest run with the previous one to identify:
        *   Fixed tests (previously failed, now passing).
        *   Regressions (previously passed, now failing).
        *   Persistently failing tests.
        *   Newly added tests.
    *   Visualizes test run history (Pass/Fail counts, Pass Rate over time).
3.  **AI-Powered Log Summarization & Analysis:**
    *   Analyze individual log files or batches of logs.
    *   Supports both:
        *   **Offline Models:** Using locally running Ollama models (e.g., Llama 3, Mistral).
        *   **Online Models:** Using Google AI Studio (Gemini models) via API key.
    *   Generates structured summaries of log content, identifying key events, errors, and test status.
4.  **Interactive Test Report Analysis Dashboard:**
    *   Upload or specify the path to a JUnit XML test report.
    *   Combines XML data with AI log summaries and associated artifacts (screenshots, page source, logs) into a unified view.
    *   Provides interactive filtering (by status, search term) and visualizations:
        *   Test duration bar chart.
        *   Pass/Fail distribution pie chart.
        *   Detailed results table with expandable failure messages.
    *   Offers a "Unified View" tab showing all details and resources (screenshots, page source, AI summary, visual analysis) for each test case.
5.  **AI-Enhanced Root Cause Analysis (RCA):**
    *   Performs RCA based on the details of failed tests identified in the "Test Report Analysis" tab.
    *   Leverages AI summaries, failure messages, log snippets, and potentially multi-perspective visual analysis (if generated) to identify potential root causes.
    *   Displays RCA findings and allows downloading the report.
6.  **Multi-Perspective Visual Analysis:**
    *   For failed tests with associated screenshots, page source, and logs, users can trigger an AI-driven visual analysis.
    *   The AI analyzes these artifacts from different perspectives (Technical, User Experience, Timing, Environmental) to provide a richer understanding of the failure context.

## 2. Setup and Installation

Follow these steps to set up and run GretahAI TestInsight:

**Prerequisites:**

*   **Python:** Version 3.9 or higher recommended.
*   **Git:** For cloning the repository (optional).
*   **Ollama:** (For offline models) Download and install Ollama from [https://ollama.com/](https://ollama.com/). Ensure Ollama is running and you have pulled the desired models (e.g., `ollama pull llama3`).

**Installation Steps:**

1.  **Clone the Repository (Optional):**
    ```bash
    git clone <repository_url>
    cd GretahAI-CaseForge # Or your project directory
    ```
2.  **Create a Virtual Environment (Recommended):**
    ```bash
    python -m venv venv
    # Activate the environment
    # Windows:
    .\venv\Scripts\activate
    # macOS/Linux:
    source venv/bin/activate
    ```
3.  **Install Dependencies:**
    Create a file named `requirements.txt` in the project root with the following content:

    ```text
    streamlit
    pandas
    ollama
    streamlit-aggrid
    google-generativeai
    plotly
    pytest
    Pillow
    ```

    Install the libraries:
    ```bash
    pip install -r requirements.txt
    ```

**Configuration:**

1.  **Google AI Studio API Key (for Online Models):**
    *   Obtain an API key from [Google AI Studio](https://aistudio.google.com/).
    *   **Option 1 (Recommended):** Create a `config.json` file in the project root directory (`c:\GenAIJira\JiraOllamaPython\GRETAH-CaseForge\config.json`) with the following structure:
        ```json
        {
          "api_key": "YOUR_GOOGLE_AI_STUDIO_API_KEY"
        }
        ```
        Replace `"YOUR_GOOGLE_AI_STUDIO_API_KEY"` with your actual key. The application will attempt to read the key from this file first.
    *   **Option 2 (Upload):** If `config.json` is not found or doesn't contain the key, the application sidebar will prompt you to upload the `config.json` file.
    *   **Option 3 (Manual Input):** As a last resort, you can paste the API key directly into the password input field in the sidebar when "Online" model type is selected. *Note: This is less secure and the key is only stored for the current session.*
2.  **Ollama Setup (for Offline Models):**
    *   Ensure the Ollama application is running in the background.
    *   Make sure you have downloaded the models you intend to use (e.g., `ollama pull llama3`). The application assumes Ollama is accessible at its default address (`http://localhost:11434`).

## 3. File Structure Explanation

*   **`Autotest/`**: Contains the core logic and files for the TestInsight application.
*   **`Autotest/GretahAI_TestInsight.py`**: The entry point for the Streamlit app.
*   **`Autotest/helper.py`**: Crucial module containing reusable functions for file handling, AI interactions, data parsing, etc.
*   **`Autotest/conftest.py`**: Configures `pytest` and provides fixtures, notably for taking screenshots during test runs. The screenshot naming convention is defined here.
*   **`Autotest/logs/test_logs/`**: Where logs generated during test execution (monitored by the app) should reside.
*   **`Autotest/raw_outputs/`**: Stores persistent data like AI summaries, RCA reports, and test run history.
*   **`Autotest/screenshots/`**: Where screenshots captured by the `conftest.py` fixture are saved.
*   **`Autotest/uploads/`**: Default location for files uploaded via the Streamlit interface.
*   **`config.json`**: Securely stores the Google AI Studio API key.

*(Note: Some top-level directories like `csv_exports`, `edited_excel`, `Test_cases` seem related to a different part of the `GRETAH-CaseForge` project and are not directly used by `GretahAI_TestInsight.py`)*

## 4. Usage Instructions (How-To Guide)

1.  **Launch the Application:**
    *   Navigate to the `Autotest` directory in your terminal:
        ```bash
        cd c:\GenAIJira\JiraOllamaPython\GRETAH-CaseForge\Autotest
        ```
    *   Ensure your virtual environment is activated.
    *   Run the Streamlit application:
        ```bash
        streamlit run GretahAI_TestInsight.py
        ```
    *   The application will open in your default web browser.

2.  **Navigate the Interface:**
    *   **Sidebar:** Contains main navigation ("Test Execution", "Test Analysis"), configuration settings (Model Type, API Key, Log Directory), file uploads, and usage stats.
    *   **Main Area:** Displays content based on the selected sidebar section and tabs within that section.

3.  **Executing Tests (`Test Execution` Section):**
    *   Go to the "Test Execution" section via the sidebar.
    *   In the sidebar's "Upload & Execute" area, click "Browse files" to upload your `pytest`-compatible test suite (`.py` file).
    *   Click "▶️ Execute Test Suite".
    *   The application will run `pytest` with the uploaded suite, generating a JUnit XML report (`results_<timestamp>.xml`) and capturing logs.
    *   **Monitoring:** Switch to the "Execution Logs" tab in the main area. New log files generated during the run will appear as expandable cards, showing their status and content.
    *   **Stopping:** If a test run is in progress, a "⏹️ Stop Execution" button appears in the sidebar. Click it to terminate the `pytest` process.
    *   **Results Comparison:** After execution finishes, go to the "Test Results Comparison" tab. It displays metrics for the latest run and compares it to the previous run, highlighting changes and trends. Test history charts are also shown here.

4.  **Analyzing Logs & Test Reports (`Test Analysis` Section):**
    *   Go to the "Test Analysis" section via the sidebar.
    *   **Configure Analysis:**
        *   Select "Model Type" (Offline/Online) in the sidebar.
        *   If "Offline", choose a model from the dropdown (ensure Ollama is running with that model).
        *   If "Online", choose a model, and ensure your Google API key is configured (via `config.json`, upload, or manual input).
        *   (Optional) Change the "Log Directory Path" if your logs aren't in the default location.
        *   (Optional) Upload specific log files directly using the file uploader. *(Note: Analysis currently focuses on XML reports; direct log upload analysis might be limited).*
    *   **Load Test Report:**
        *   Go to the "Test Report Analysis" tab in the main area.
        *   Choose the XML source: "Upload File" or "Enter File Path".
        *   Upload the `results.xml` (or similarly named) file generated by `pytest`, or provide the correct path.
        *   The dashboard will load, displaying overall metrics (Total, Passed, Failed, Duration).
    *   **Interact with Dashboard:**
        *   **Filters (Sidebar):** Filter the displayed tests by status (Passed/Failed) or search by name/class.
        *   **Insights:** View quick insights like average test time, slowest test, and the class with the most failures.
        *   **Visualizations Tab:**
            *   *Test Duration:* Bar chart of the slowest tests.
            *   *Pass/Fail Distribution:* Pie chart showing the overall status breakdown.
            *   *Detailed Results:* Interactive table (AgGrid) listing all tests. Click a row to see details; if failed, the stack trace is shown.
            *   *Unified View:* A comprehensive view for each test. Expand a test to see tabs for:
                *   *Details:* Failure message/stack trace.
                *   *Screenshot:* Displays the associated screenshot if found.
                *   *Page Source:* Displays the associated page source HTML if found (with download).
                *   *AI Summary:* Shows the AI-generated summary if a matching log was analyzed.
                *   *Visual Analysis:* If resources are available, click "Generate Visual Analysis" to get AI insights based on screenshot, page source, and logs. Results are cached per session.
    *   **Perform Root Cause Analysis (RCA):**
        *   Go to the "Root Cause Analysis" tab.
        *   Ensure you have loaded an XML report in the "Test Report Analysis" tab first, as RCA uses the failed tests identified there.
        *   Click the "Perform RCA" button.
        *   The application gathers details for all failed tests (failure messages, AI summaries, log snippets, visual analysis if available) and sends them to the selected AI model.
        *   The AI's consolidated RCA findings are displayed.
        *   You can download the RCA report as a text or JSON file.

## 5. Helper Functions Documentation (`helper.py`)

*(Note: This documentation is based on the function names and their usage within `GretahAI_TestInsight.py`, as `helper.py` itself was not provided.)*

**File Handling & Information:**

*   `get_log_files(log_dir_path)`: Scans the specified directory for log files (likely `.log` or `.txt`). Returns a list of file paths.
*   `is_binary_file(filepath)`: Checks if a given file is likely a binary file (to avoid reading non-text logs). Returns `True` or `False`.
*   `read_log_file(filepath, max_length)`: Reads the content of a text file, potentially truncating it to `max_length`. Returns the file content as a string or an error message.
*   `save_summary(summary_data, filename, output_dir)`: Saves the provided summary data (likely a dictionary or string) to a file in the specified output directory.
*   `check_directory_contents(dir_path)`: Checks if a directory exists and is not empty. Returns `True` or `False`.
*   `list_latest_logs(log_dir_path, since_timestamp)`: Lists log files in a directory modified after a given timestamp. Used for real-time log monitoring during test execution. Returns a list of filenames.
*   `get_log_status(log_filepath)`: Determines the status (e.g., PASS/FAIL) based on the content or name of a log file. (Implementation details inferred). Returns a status string.
*   `format_file_size(size_bytes)`: Converts a file size in bytes to a human-readable format (KB, MB, etc.). Returns a formatted string.
*   `generate_csv(...)`: Likely generates a CSV file from given data (details inferred, might be related to the other module).

---

## Commercial Licensing & Enterprise Support

**GretahAI TestInsight is proprietary commercial software developed by Cogniron.**

### Commercial Licensing

- **License Type**: Commercial/Proprietary License
- **Usage Rights**: Valid commercial license required for all use
- **Distribution**: Unauthorized distribution prohibited
- **Modifications**: Source code modifications require explicit written permission
- **Enterprise Analytics**: Advanced analytics features available with enterprise licensing

### Enterprise Support & Services

**Primary Support Contact**: <EMAIL>

**Commercial Services Available**:
- Enterprise licensing and deployment
- Custom analytics and reporting development
- Professional training and implementation services
- Dedicated technical support with guaranteed response times
- Integration consulting for existing testing infrastructure
- Advanced AI model customization and optimization

### Enterprise Features

- Custom dashboard development
- Advanced AI model integration
- Enterprise-grade security and compliance
- Multi-tenant deployment options
- Custom reporting and analytics
- Priority feature development

### Contact Information

- **Website**: https://cogniron.com
- **Primary Contact**: <EMAIL>
- **Commercial Licensing**: Contact for pricing and enterprise licensing options
- **Enterprise Support**: Dedicated support packages available for commercial customers

---

**© 2025 Cogniron. All Rights Reserved.**

**PROPRIETARY COMMERCIAL SOFTWARE** - This software is proprietary and confidential. Unauthorized copying, distribution, modification, or use of this software is strictly prohibited.

**Note**: This software requires a valid commercial license for use. Enterprise evaluation licenses and proof-of-concept deployments are available upon request for qualified customers.


